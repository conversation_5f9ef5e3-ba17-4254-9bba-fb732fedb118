---
description: 
globs: 
alwaysApply: true
---
If you encounter Linter errors or warnings:

1. **Diagnose:** Run `npx eslint <file>` to get the error/warning details (message, rule name).

2. **Investigate & Configure:**
   * **Research:** Look up the ESLint rule (check docs or **perform a web search**) to understand its purpose, configuration options, and if it's auto-fixable.
   * **ESLint Configuration:** Update `.eslintrc.cjs` to ensure:
      - The rule is properly configured with the correct severity level
      - Any required plugins are added to both the "plugins" and "extends" arrays
      - For the specific rule, add appropriate configuration in the "rules" section
      - If needed, add rule-specific overrides in the "overrides" section for certain file patterns
   * **VS Code Settings:** Verify `.vscode/settings.json` contains these exact settings:
      ```json
      "editor.formatOnSave": true,
      "editor.defaultFormatter": "dbaeumer.vscode-eslint",
      "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "always",
        "source.fixAll": "always"
      }
      ```

3. **Apply Fix:**
   * **Test Auto-Fix:** Run `npx eslint <file> --fix` to verify the rule can be auto-fixed
   * **Configure for Auto-Fix:** If the rule is auto-fixable but not being fixed on save:
     - Ensure the rule severity is "warn" or "error" (not "off")
     - Check if the rule requires specific plugin options in `.eslintrc.cjs`
     - Verify the rule isn't being disabled in any "overrides" section
   * **Manual Configuration:** If a rule can't be auto-fixed, add appropriate rule configuration to reduce friction

4. **Verify and Document:**
   * Run `npx eslint <file>` again to confirm the error is resolved
   * Document any configuration changes made to `.eslintrc.cjs` or `.vscode/settings.json`
   * Consider adding the specific rule to an appropriate override section if it should only apply to certain files

**Goal:** Ensure every fixable lint error is automatically corrected on save by properly configuring both ESLint and VS Code settings. For non-auto-fixable issues, provide clear configuration guidance to minimize their occurrence.

After you are done resolving errors/warnings, ensure that the `.eslintrc.cjs` and `.vscode/settings.json` have been updated properly to ensure that those same linter issues will auto fix in the future.

Please do not cheat by disabling rules/warnings - you need to resolve them properly.
